from typing import TypedDict, List, Optional, Dict, Any
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from datetime import datetime, timedelta
import json
import re
from config import storage, MEAL_KIND_MAPPING, DATE_KEYWORDS, OPENAI_API_KEY, OPENAI_BASE_URL

class MealState(TypedDict):
    """餐食状态定义"""
    messages: List[BaseMessage]
    meal_name: Optional[str]
    meal_kind: Optional[str] 
    meal_date: Optional[str]
    exist: Optional[bool]
    search_results: Optional[List[str]]
    selected_meals: Optional[List[str]]
    session_id: str
    current_step: str

class CozyMealAgent:
    def __init__(self):
        # 初始化LLM
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.3,
            api_key=OPENAI_API_KEY,
            base_url=OPENAI_BASE_URL
        )
        
        # 创建状态图
        self.workflow = self._create_workflow()
        self.memory = MemorySaver()
        self.app = self.workflow.compile(checkpointer=self.memory)
        
        # 画图
        # self.app.get_graph(xray=True).draw_mermaid_png(output_file_path="meal_agent.png")
    
    def _create_workflow(self) -> StateGraph:
        """创建LangGraph工作流"""
        workflow = StateGraph(MealState)
        
        # 添加节点
        workflow.add_node("analyze_input", self._analyze_input_node)
        workflow.add_node("search_meals", self._search_meals_node) 
        workflow.add_node("collect_info", self._collect_info_node)
        workflow.add_node("confirm_meal", self._confirm_meal_node)
        workflow.add_node("add_meal", self._add_meal_node)
        
        # 添加边
        workflow.add_edge(START, "analyze_input")
        workflow.add_conditional_edges(
            "analyze_input",
            self._route_after_analysis,
            {
                "search": "search_meals",
                "collect": "collect_info", 
                "confirm": "confirm_meal",
                "add": "add_meal"
            }
        )
        workflow.add_edge("search_meals", "collect_info")
        workflow.add_edge("collect_info", END)
        workflow.add_edge("confirm_meal", "add_meal")
        workflow.add_edge("add_meal", END)
        
        return workflow
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是Cozy，一个智能的饮食日程助手。你需要帮助用户安排餐食计划。

核心功能：
1. 收集用户餐食信息：meal_name, meal_kind, meal_date
2. 搜索现有餐食或创建自定义餐食
3. 确认并添加餐食到用户计划

交互规则：
- 信息不全时需要追问
- 搜索到餐食时提供选择
- 确认后添加到用户计划
- 始终保持友好和专业

限制：
- 自定义餐食最多100个
- 单餐最多50道菜
- 超出限制时给出相应提示

请根据用户输入分析意图并执行相应操作。"""

    def _analyze_input_node(self, state: MealState) -> MealState:
        """分析用户输入节点"""
        messages = [SystemMessage(content=self._get_system_prompt())] + state["messages"]
        
        # 分析用户意图和提取信息
        analysis_prompt = """
分析用户输入，提取以下信息：
1. meal_name: 餐食名称
2. meal_kind: 餐食类型 (breakfast/lunch/dinner/snack)
3. meal_date: 日期 (YYYY-MM-DD格式)
4. 用户意图: 添加餐食/修改计划/询问建议

请以JSON格式返回分析结果。
"""
        
        response = self.llm.invoke(messages + [HumanMessage(content=analysis_prompt)])
        
        # 解析响应并更新状态
        try:
            # 简化处理：直接从用户消息中提取关键信息
            user_message = state["messages"][-1].content.lower()
            
            # 提取餐食名称
            meal_name = self._extract_meal_name(user_message)
            
            # 提取餐食类型
            meal_kind = self._extract_meal_kind(user_message)
            
            # 提取日期
            meal_date = self._extract_meal_date(user_message)
            
            state.update({
                "meal_name": meal_name,
                "meal_kind": meal_kind,
                "meal_date": meal_date,
                "current_step": "analyzed"
            })
            
        except Exception as e:
            print(f"分析错误: {e}")
            state["current_step"] = "error"
        
        return state
    
    def _extract_meal_name(self, text: str) -> Optional[str]:
        """从文本中提取餐食名称"""
        # 简化实现：查找常见餐食关键词
        meal_keywords = ["avocado", "toast", "salad", "pie", "chicken", "potato"]
        for keyword in meal_keywords:
            if keyword in text:
                return keyword
        return None
    
    def _extract_meal_kind(self, text: str) -> Optional[str]:
        """从文本中提取餐食类型"""
        for kind, keywords in MEAL_KIND_MAPPING.items():
            for keyword in keywords:
                if keyword in text:
                    return kind
        return None
    
    def _extract_meal_date(self, text: str) -> Optional[str]:
        """从文本中提取日期"""
        today = datetime.now()
        
        if "today" in text:
            return today.strftime("%Y-%m-%d")
        elif "tomorrow" in text:
            return (today + timedelta(days=1)).strftime("%Y-%m-%d")
        elif "monday" in text:
            return self._get_next_weekday(today, 0).strftime("%Y-%m-%d")
        # 添加更多日期解析逻辑...
        
        return None
    
    def _get_next_weekday(self, date: datetime, weekday: int) -> datetime:
        """获取下一个指定星期几的日期"""
        days_ahead = weekday - date.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        return date + timedelta(days_ahead)
    
    def _search_meals_node(self, state: MealState) -> MealState:
        """搜索餐食节点"""
        meal_name = state.get("meal_name", "")
        
        # 在预设餐食中搜索
        search_results = []
        for preset_meal, details in storage.preset_meals.items():
            if meal_name.lower() in preset_meal.lower() or any(meal_name.lower() in ing.lower() for ing in details["ingredients"]):
                search_results.append(preset_meal)
        
        # 限制返回前5个结果
        search_results = search_results[:5]
        
        state.update({
            "search_results": search_results,
            "exist": len(search_results) > 0,
            "current_step": "searched"
        })
        
        return state
    
    def _collect_info_node(self, state: MealState) -> MealState:
        """收集信息节点"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind") 
        meal_date = state.get("meal_date")
        search_results = state.get("search_results", [])
        
        # 生成响应消息
        if not meal_name:
            response = "What would you like to eat?"
        elif not meal_date and not meal_kind:
            response = "When would you like to plan this meal?"
        elif not meal_kind:
            response = "Is this for breakfast, lunch, dinner, or a snack?"
        elif not meal_date:
            response = "Which day would you like this meal for?"
        elif search_results:
            response = f"Got it! We have {', '.join(search_results)} available for you. Which one would you like?"
        else:
            response = f"Got it, but '{meal_name}' is not in our database. Do you want me to create the meal first, then add it?"
        
        state["messages"].append(AIMessage(content=response))
        state["current_step"] = "collected"
        
        return state
    
    def _confirm_meal_node(self, state: MealState) -> MealState:
        """确认餐食节点"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind")
        meal_date = state.get("meal_date")
        
        # 生成确认消息
        confirmation = f"Awesome! The scheduled meal details are listed here:\n"
        confirmation += f"Meal: {meal_name}\n"
        confirmation += f"Type: {meal_kind}\n" 
        confirmation += f"Date: {meal_date}\n"
        confirmation += "Would you like to confirm?"
        
        state["messages"].append(AIMessage(content=confirmation))
        state["current_step"] = "confirmed"
        
        return state
    
    def _add_meal_node(self, state: MealState) -> MealState:
        """添加餐食节点"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind")
        meal_date = state.get("meal_date")
        exist = state.get("exist", False)
        session_id = state.get("session_id", "default")
        
        # 检查限制
        if not exist and len(storage.custom_meals) >= 100:
            response = "You've created 100 personalized meals – that's the limit for now! To manage your recipes, go to My Meals."
            state["messages"].append(AIMessage(content=response))
            return state
        
        meal_key = f"{meal_date}_{meal_kind}"
        current_meals = storage.meal_plans.get(meal_key, [])
        
        if len(current_meals) >= 50:
            response = "Your meal plan is looking great with 50 dishes already. To add anything new, please remove a few first."
            state["messages"].append(AIMessage(content=response))
            return state
        
        # 添加餐食
        try:
            if not exist:
                # 创建自定义餐食
                storage.custom_meals[meal_name] = {"created_by": session_id, "date_created": datetime.now().isoformat()}
            
            # 添加到计划
            if meal_key not in storage.meal_plans:
                storage.meal_plans[meal_key] = []
            storage.meal_plans[meal_key].append(meal_name)
            
            response = "Your meal has been added. Enjoy!"
            
        except Exception as e:
            response = "Sorry, there has been something wrong. Would you like to try again?"
        
        state["messages"].append(AIMessage(content=response))
        state["current_step"] = "completed"
        
        return state
    
    def _route_after_analysis(self, state: MealState) -> str:
        """分析后的路由决策"""
        meal_name = state.get("meal_name")
        meal_kind = state.get("meal_kind")
        meal_date = state.get("meal_date")
        
        # 检查用户是否在确认
        last_message = state["messages"][-1].content.lower()
        if "confirm" in last_message or "yes" in last_message:
            return "confirm"
        
        # 如果信息完整且有餐食名称，进行搜索
        if meal_name and not state.get("search_results"):
            return "search"
        
        # 如果信息不完整，收集信息
        if not meal_name or not meal_kind or not meal_date:
            return "collect"
        
        # 默认收集信息
        return "collect"

# 创建全局代理实例
cozy_agent = CozyMealAgent()