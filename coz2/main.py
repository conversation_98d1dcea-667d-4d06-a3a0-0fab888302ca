from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, AsyncGenerator
import json
import uuid
from langchain_core.messages import HumanMessage
from coz2.meal_agent import cozy_agent, MealState

app = FastAPI(title="Cozy Meal Assistant API", version="1.0.0")

class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str
    session_id: Optional[str] = None
    selected_meals: Optional[list] = None

class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    session_id: str
    meal_info: Optional[dict] = None

@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    """聊天接口 - 流式响应"""
    try:
        # 生成或使用现有session_id
        session_id = request.session_id or str(uuid.uuid4())
        
        # 创建初始状态
        initial_state: MealState = {
            "messages": [HumanMessage(content=request.message)],
            "meal_name": None,
            "meal_kind": None,
            "meal_date": None,
            "exist": None,
            "search_results": None,
            "selected_meals": request.selected_meals,
            "session_id": session_id,
            "current_step": "start"
        }
        
        # 配置
        config = {"configurable": {"thread_id": session_id}}
        
        async def generate_response() -> AsyncGenerator[str, None]:
            """生成流式响应"""
            try:
                # 调用LangGraph应用
                result = await cozy_agent.app.ainvoke(initial_state, config)
                
                # 获取最后的AI消息
                ai_messages = [msg for msg in result["messages"] if hasattr(msg, 'content') and msg.__class__.__name__ == 'AIMessage']
                
                if ai_messages:
                    response_content = ai_messages[-1].content
                    
                    # 构建响应数据
                    response_data = {
                        "response": response_content,
                        "session_id": session_id,
                        "meal_info": {
                            "meal_name": result.get("meal_name"),
                            "meal_kind": result.get("meal_kind"),
                            "meal_date": result.get("meal_date"),
                            "exist": result.get("exist"),
                            "search_results": result.get("search_results"),
                            "current_step": result.get("current_step")
                        }
                    }
                    
                    # 流式输出
                    for char in response_content:
                        yield f"data: {json.dumps({'char': char, 'type': 'char'})}\n\n"
                    
                    # 发送完整响应
                    yield f"data: {json.dumps({'response': response_data, 'type': 'complete'})}\n\n"
                else:
                    yield f"data: {json.dumps({'error': 'No response generated', 'type': 'error'})}\n\n"
                    
            except Exception as e:
                error_msg = f"Sorry, I encountered an error: {str(e)}"
                yield f"data: {json.dumps({'error': error_msg, 'type': 'error'})}\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "Cozy Meal Assistant"}

@app.get("/meals/plans")
async def get_meal_plans():
    """获取所有餐食计划"""
    from config import storage
    return {"meal_plans": storage.meal_plans}

@app.get("/meals/custom")
async def get_custom_meals():
    """获取自定义餐食"""
    from config import storage
    return {"custom_meals": storage.custom_meals}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)